{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "arielmejiadev/larapex-charts": "^6.0", "consoletvs/charts": "^6.6", "endroid/qr-code": "^5.0", "guzzlehttp/guzzle": "^7.2", "josiasmontag/laravel-recaptchav3": "^1.0", "laravel/framework": "^9.11", "laravel/sanctum": "^2.14.1", "laravel/tinker": "^2.7", "laravel/ui": "^3.2", "livewire/livewire": "^2.12", "lotuashvili/laravel-tbcpay": "*", "maatwebsite/excel": "^3.1", "mailchimp/marketing": "^3.0", "mcamara/laravel-localization": "^2.0", "mediconesystems/livewire-datatables": "^0.10.1", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/laravel-ignition": "^1.6", "spatie/laravel-permission": "^6.0", "spatie/laravel-translatable": "^6.9"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}