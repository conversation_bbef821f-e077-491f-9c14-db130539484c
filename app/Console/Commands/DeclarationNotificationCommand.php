<?php

namespace App\Console\Commands;

use App\Models\goods;
use App\Models\ParcelStatusLog;
use App\Models\SMSslider;
use App\Support\SMS;
use Illuminate\Console\Command;

class DeclarationNotificationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'declaration:notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $goodIds = ParcelStatusLog::query()
            ->where('created_at', '<', now()->subDays(2))
            ->where('status', 'SENT')
            ->pluck('good_id')
        ;

        // შევამოწმოთ არის თუ არა ID-ები
        if ($goodIds->isEmpty()) {
            $this->info('No goods found to process');
            return 0;
        }

        $this->info("Processing {$goodIds->count()} goods...");

        $text = SMSslider::query()->first();

        // თუ SMS ტექსტი არ მოიძებნა
        if (!$text) {
            $this->error('SMS text not found');
            return 1;
        }

        // ID-ების chunk-ებად დაყოფა (1000-ზე ნაკლები)
        $goodIds->chunk(1000)->each(function ($chunk) use ($text) {
            $parcels = goods::query()
                ->with('user')
                ->select(['user_id', 'id'])
                ->whereIn('id', $chunk->toArray())
                ->get();

            foreach ($parcels as $parcel) {
                $user = $parcel->user;

                // შევამოწმოთ user არსებობს თუ არა
                if ($user && $user->phone) {
                    new SMS($user->phone, $text->received_two_day);
                    $this->info("SMS sent to user {$user->id}");
                } else {
                    $this->warn("User or phone not found for parcel {$parcel->id}");
                }
            }
        });

        $this->info('Done');
        return 0;
    }
}