<?php

namespace App\Http\Livewire;

use App\Models\goods;
use Livewire\Component;
use Livewire\WithPagination;

class Packages extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';

    public $categories = [];
    public $sortColumn = 'id';
    public $sortDirection = 'asc';
    public $searchColumns = [
        'id' => '',
//        'price' => ['', ''],
//        'description' => '',
//        'product_category_id' => 0,
    ];

    public function mount()
    {
        $this->categories = goods::pluck( 'id');
    }

    public function sortByColumn($column)
    {
        if ($this->sortColumn == $column) {
            $this->sortDirection = $this->sortDirection == 'asc' ? 'desc' : 'asc';
        } else {
            $this->reset('sortDirection');
            $this->sortColumn = $column;
        }
    }

    public function updating($value, $name)
    {
        $this->resetPage();
    }

    public function render()
    {
        $products = Product::select([
            'products.id',
//            'price',
//            'description',
//            'product_categories.name as category_name',
//            'product_category_id',
        ])
            ->leftJoin('product_categories',
                'products.product_category_id',
                '=',
                'product_categories.id');

        foreach ($this->searchColumns as $column => $value) {
            if (!empty($value)) {
                if ($column == 'price') {
                    if (is_numeric($value[0])) {
                        $products->where($column, '>', $value[0]);
                    }
                    if (is_numeric($value[1])) {
                        $products->where($column, '<', $value[1]);
                    }
                } else if ($column == 'product_category_id') {
                    $products->where($column, $value);
                } else {
                    $products->where('products.' . $column, 'LIKE', '%' . $value . '%');
                }
            }
        }

        $products->orderBy($this->sortColumn, $this->sortDirection);

        return view('livewire.packages', [
            'products' => $products->paginate(5)
        ]);
    }

}
