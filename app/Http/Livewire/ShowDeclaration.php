<?php

namespace App\Http\Livewire;

use App\Models\Branch;
use App\Models\Flight;
use App\Models\goods;
use Illuminate\Support\Carbon;
use Mediconesystems\LivewireDatatables\BooleanColumn;
use Mediconesystems\LivewireDatatables\Column;
use Mediconesystems\LivewireDatatables\DateColumn;
use Mediconesystems\LivewireDatatables\Http\Livewire\LivewireDatatable;
use Mediconesystems\LivewireDatatables\NumberColumn;
use Mediconesystems\LivewireDatatables\TimeColumn;

class ShowDeclaration extends LivewireDatatable
{
    protected $appends = ['Undeclared'];
    public $complex = true;
    public $exportable = true;
    public  $bulkDisabled = true;
    public  $GoodsStatus;

    public function builder()
    {
        //
        return goods::query()
            ->leftJoin('flights', 'flights.id', 'goods.flight_id')
            ->leftJoin('users', 'users.id', 'goods.user_id')
            ->leftJoin('branches', 'branches.id', 'goods.branch_id')
            ->orderBy('took_out_date', 'desc')
//            ->orderBy('users.id', 'desc')
            ->where('uses_courier', '=', 1)
            ->where('goods.flight_parcel_state', '!=', 'TOOK_OUT');
    }

    public function columns()
    {
        return [


            Column::checkbox(),


            NumberColumn::name('id')
                ->label('ID')
                ->filterable()
                ->linkTo('admin/parcels/edit', 6),

            DateColumn::name('took_out_date')
                ->label('საკურიეროს მონიშვნის თარიღი')
                ->format('Y-m-d H:i:s')
                ->filterable(),
            Column::name('delivery_comment')
                ->label('მომხმარებლის კომენტარი')
                ->filterable(),


//            TimeColumn::name('took_out_date1')
//                ->label('Go to bed')
//                ->filterable(),
//
//
//            Column::callback('took_out_date1', 'computeBedtime')
//                ->label('Go to bed')
//                ->hide(),
            Column::name('tracking_code')
                ->label('კოდი')
                ->filterable()
                ->searchable(),

//            Column::name('file_path')
//                ->label('საკურიეროს სტატუსი')
//                ->filterable()
//                ->searchable(),

            Column::name('file_path')
                ->label('საკურიეროს სტატუსი')
//                ->filterable()
                ->filterable(['გაკეთებული', 'გასაკეთებელი', 'SENT', 'WAITING', 'NONE'])
//                ->filterable($this->goods->pluck('flight_parcel_state'))
                ->searchable(),
            Column::name('rec_name')
                ->label('მომხმარებელი')
                ->filterable()
                ->searchable(),
            Column::name('pid')
                ->label('პირადი/საიდენტიფიკაციო ნომერი')
                ->filterable()
                ->searchable(),
            Column::name('users.phone')
                ->filterable()
                ->label('ტელეფონი')
                ->searchable(),
            Column::name('address_1')
                ->filterable()
                ->label('მისამართი')
                ->searchable(),
            Column::name('address_2')
                ->filterable()
                ->label('მისამართი/ქალაქი')
                ->searchable(),

            Column::name('room_number')
                ->filterable()
                ->label('ოთახის ნომერი')
                ->searchable(),

            Column::name('flights.flight_number')
                ->filterable()
                ->label('ფრენის ნომერი')
                ->searchable(),

            DateColumn::name('flights.takeout_date')
                ->label('ფრენის თარიღი')
                ->filterable(),

            NumberColumn::name('phisicalw')
                ->label('წონა')
                ->searchable(),
            NumberColumn::name('price_to_pay')
                ->label('თანხა')
                ->searchable(),
            Column::name('flight_parcel_state')
                ->label('ამანათის სტატუსი')
//                ->filterable()
                ->filterable(['RECIEVED', 'TOOK_OUT', 'SENT', 'WAITING', 'NONE'])
//                ->filterable($this->goods->pluck('flight_parcel_state'))
                ->searchable(),


            BooleanColumn::name('is_payed')
                ->label('გადახდა')
                ->filterable(),
            BooleanColumn::name('is_declared')
                ->label('დეკლარაცია')
                ->filterable(),
            BooleanColumn::name('must_customize')
                ->label('განბაჟება')
                ->filterable(),
            BooleanColumn::name('uses_courier')
                ->label('საკურიერო')
                ->filterable(),

            Column::name('branches.title_en')
                ->label('branch_id')
                ->filterable($this->branches)
//                ->filterable()
//                ->filterable($this->goods->pluck('flight_parcel_state'))
                ->searchable(),

//            Column::callback(['id', 'pid'], function ($id, $pid) {
//                return view('livewire.datatables.datatable', ['id' => $id, 'pid' => $pid]);
//            })

//            Column::callback(['id', 'pid'], function ($id, $pid) {
//                return view('livewire.datatables.table-action', ['id' => $id, 'pid' => $pid]);
//            })
//            Column::delete(),
            Column::callback(['id', 'pid'], function ($id, $pid) {

                return view('livewire.datatables.courier-table-actions',
                    [
                        'id' => $id,
                        'pid' => $pid
                    ]);

            })

        ];
    }

//    public function getPlanetsProperty()
//    {
//        return Planet::pluck('name');
//    }


    public function tetece($id)
    {
        $good = goods::query()->find($id);
        $good->uses_courier = false;
        $good->address_1 = null;
        $good->delivery_comment = null;
        $good->address_2 = null;
        $good->took_out_date = null;
        $good->file_path = null;
        $good->save();
    }

    public function getFlightsProperty()
    {
        return Flight::pluck('flight_parcel_state');
    }
    public function getBranchesProperty()
    {
        return Branch::pluck('title_en');
    }
    public function computeBedtime($date)
    {
        if (!$date) {
            return;
        }
        return Carbon::parse($date)->isPast()
            ? Carbon::parse($date)->addDay()->diffForHumans(['parts' => 2])
            : Carbon::parse($date)->diffForHumans(['parts' => 2]);
    }
//    public function delete($id)
//    {
//        $product = Goods::find($id);
//        if ($product) {
//            $product->delete();
//        }
////        dd($product);
//    }
}
