<div>
    {{-- Because she competes with no one, no one can compete with her. --}}
    <div>
        <label class="">INSERT TRACKING NUMBER</label>
        <input type="text"
               wire:model.debounce.500ms="searchTerm"
               wire:keydown.enter="search"
               class="form-control"/>
    {{--    <button wire:click="search" class="btn btn-primary">ძებნა</button>--}}
        @if (session()->has('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        @if ($foundTrackingCode)
            <br>
            <p class="text-dark mb-4 geo" style="font-weight: bold">
                კომენტარი: <span style="color: red;">{{ $foundTrackingCode->admin_comment}}</span>
            </p>
            <p class="text-dark mb-4 geo">{{ $foundTrackingCode->tracking_code }}</p>



{{--            <p class="text-lg">time{{ $foundTrackingCode->sent_from_china_date }}</p>--}}
            <p class="text-dark mb-4 geo">{{ $foundTrackingCode->rec_name }}</p>
            <p class="text-dark mb-4 geo">{{ $foundTrackingCode->room_number }}</p>
{{--            <p class="text-lg">{{ $foundTrackingCode->flight_parcel_state }}</p>--}}
            <p class="text-dark mb-4 geo">
                {{ $foundTrackingCode->Branch->title_en}}
            </p>

            <p class="text-dark mb-4 geo">
                {{ 'რეისი: '.$foundTrackingCode->flight->flight_number}}
            </p>

            <p class="text-dark mb-4 geo">
                {{ 'წონა: '.$foundTrackingCode->phisicalw}}
            </p>

            <p class="text-dark mb-4 geo">
                {{ 'ამანათების რაოდენობა: '.$userParcelCount}}
            </p>

            <p class="text-dark mb-4 geo">
                {{ 'სულ გასატანი ამანათები: '.$goodsReportsTotal}}
            </p>

            <p class="mb-4 geo">ID {{ $foundTrackingCode->small_comment.' index: '.$scannedRank }}</p>

            @if($foundTrackingCode->customs_clearance)
                <p class="mb-4 text-green-600 geo">
                    @lang('განბაჟებული')
                </p>
            @else
                <p style="color: red" class="mb-4 geo">
                    @lang('განუბაჟებელი')
                </p>
            @endif

            {{--            <p class="text-lg">წონა : {{ $foundTrackingCode->phisicalw }}</p>--}}
{{--            <p class="text-lg">ამანათის ღირებულება : {{ $foundTrackingCode->client_buy_amount }}</p>--}}

    {{--        <input type="text" wire:model="good_id" class="form-control">--}}
    {{--        <button wire:click="update">Update</button>--}}
        @endif
    </div>
</div>
