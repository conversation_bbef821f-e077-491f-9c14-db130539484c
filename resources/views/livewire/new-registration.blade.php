<div class=" container ">
    <div class="card card-custom gutter-b example example-compact">
        <div class="card-header">
            <h3 class="card-title geo">
                @lang('trans.register')
            </h3>
        </div>
        <!--begin::Form-->
        <form  wire:submit.prevent="submit">
            <div class="card-body">
                {{--            პირველი სვეტი--}}
                <div class="form-group row">
                    <div class="col-lg-4">
                        <label class="geo">@lang('trans.acc_type')</label>
                        <div class="radio-inline geo">
                            <label class="radio radio-solid">
                                <input wire:model="show" type="radio" name="example_2" value="true"/>
                                <span></span>
                                @lang('trans.legal_user')
                            </label>
                            <label class="radio radio-solid">
                                <input wire:click="$set('show', false)"  type="radio" name="example_2" value="false" checked="checked"/>
                                <span></span>
                                @lang('trans.physical_user')
                            </label>
                        </div>
                    </div>
                </div>


                <div class="form-group row">
                    <div class="col-lg-4">
                        <label class="geo">@lang('trans.email')</label>
                        <div class="input-group ">
                            <div class="input-group-prepend is-valid"><span class="input-group-text"><i class="la la-user"></i></span></div>
                            <input wire:model.defer="username" type="email"  class="form-control   @error('username') is-invalid @enderror" placeholder="" />
                        </div>
                        @error('username') <span class="geo text-danger error">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-lg-4">
                        <label class="geo">@lang('trans.password')</label>
                        <input wire:model.defer="password" type="password"  class="form-control @error('password') is-invalid @enderror" placeholder="">
                        @error('password') <span class="geo text-danger error">{{ $message }}</span> @enderror

                    </div>
                    <div class="col-lg-4">
                        <label class="geo">@lang('trans.password_repeat')</label>
                        <input wire:model.defer="password_confirmation" type="password"
                               class="form-control @error('password_confirmation') is-invalid @enderror" placeholder="">
                        @error('password_confirmation') <span class="geo text-danger error">{{ $message }}</span> @enderror

                    </div>
                </div>
                {{--            მეორე სვეტი--}}
                <div class="form-group row">
                    <div class="col-lg-3">
                        <label class="geo">@lang('trans.first_name_ge')</label>
                        <input wire:model="first_name_ge" type="text"
                                class=" form-control @error('first_name_ge') is-invalid @enderror"
                                placeholder="">
                        @error('first_name_ge') <span class="geo text-danger error">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-lg-3">
                        <label class="geo">@lang('trans.last_name_ge')</label>
                        <input wire:model="last_name_ge" type="text"
                               class=" form-control @error('last_name_ge') is-invalid @enderror"
                                placeholder="">
                        @error('last_name_ge') <span class="geo text-danger error">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-lg-3">
                        <label class="geo">@lang('trans.first_name_en')</label>
                        <input wire:model.defer="first_name_en" type="text" class="form-control @error('first_name_en') is-invalid @enderror"
                               placeholder="">
                        @error('first_name_en') <span class="geo text-danger error">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-lg-3">
                        <label class="geo">@lang('trans.last_name_en')</label>
                        <input wire:model.defer="last_name_en" type="text" class="form-control @error('last_name_en') is-invalid @enderror"
                               placeholder="">
                        @error('last_name_en') <span class="geo text-danger error">{{ $message }}</span> @enderror
                    </div>

                </div>
                {{--                მესამე სვეტი--}}
                <div class="form-group row">
                    <div class="col-lg-3">
                        @if($show)
                            <label class="geo">@lang('trans.company_identification')</label>
                        @else
                            <label class="geo">@lang('trans.personal_id')</label>
                        @endif

                        <input wire:model.defer="identification" type="text"  class="form-control @error('identification') is-invalid @enderror"
                               placeholder="">
                        @error('identification') <span class="geo text-danger error">{{ $message }}</span> @enderror
                    </div>
{{--                    @if(!$show)--}}
{{--                    <div class="col-lg-3">--}}
{{--                            <label class="geo">პირადი ნომერი</label>--}}
{{--                        <input wire:model="identification" type="text"  class="form-control @error('identification') is-invalid @enderror"--}}
{{--                               placeholder="Enter full name">--}}
{{--                        @error('identification') <span class="geo text-danger error">{{ $message }}</span> @enderror--}}
{{--                    </div>--}}
{{--                    @endif--}}

{{--                    @if($show)--}}
{{--                    <div class="col-lg-3">--}}
{{--                            <label class="geo">კომპანიის საიდენტიფიკაციო</label>--}}
{{--                        <input wire:model="company_identification" type="text"  class="form-control--}}
{{--                                @error('company_identification') is-invalid @enderror"--}}
{{--                               placeholder="Enter full name">--}}
{{--                        @error('company_identification') <span class="geo text-danger error">{{ $message }}</span> @enderror--}}
{{--                    </div>--}}
{{--                    @endif--}}


                @if($show)
                        <div class="col-lg-3">
                            <label class="geo">@lang('trans.company_name')</label>
                            <input wire:model.defer="company_name" type="text"
                                   class="ka_input form-control @error('company_name') is-invalid @enderror"
                                   placeholder="">
                            @error('company_name') <span class="geo text-danger error">{{ $message }}</span> @enderror
                        </div>
                    @else

                    @endif

                    <div class="col-lg-3">
                        <label class="geo">@lang('trans.address')</label>
                        <input wire:model.defer="address_1" type="text" class="form-control @error('address_1') is-invalid @enderror"
                               placeholder="">
                        @error('address_1') <span class="geo text-danger error">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-lg-3">
                        <label class="geo">@lang('trans.mobile_phone')</label>
                        <input wire:model.defer="phone" type="text"  class="form-control @error('phone') is-invalid @enderror"
                               placeholder="">
                        @error('phone') <span class="geo text-danger error">{{ $message }}</span> @enderror
                    </div>

                </div>
                <div class="form-group row">

                    <div class="col-lg-3">
                        <label class="geo">@lang('trans.city')</label>
                        <select wire:model.defer="city_id" class="form-control " data-size="6" data-live-search="true"
                                 name="city_id">
                            <option value="" selected>@lang('trans.select_city')</option>
                        @foreach($cities as $city)
                                <option value="{{$city->id}}">{{$city->title_ge}}</option>
                            @endforeach
                        </select>
                        @error('city_id') <span class="geo text-danger error">{{ $message }}</span> @enderror
                    </div>


                    <div class="col-lg-3">
                        <label class="geo">@lang('trans.select_branch')</label>
                        <select wire:model.lazy="branch_id" class="form-control" data-size="6" data-live-search="true"
                                name="branch_id">
                            @foreach($branches as $branch)
                                <option  value="{{$branch->id}} ">{{$branch->title_en}}</option>
                            @endforeach
                        </select>
                        @error('branch_id') <span class="geo text-danger error">{{ $message }}</span> @enderror


                    </div>


                    {{--                partniori kompaniis statusi gauqmebulia--}}
{{--                    <div class="col-lg-3">--}}
{{--                        <label for="flight_status">პარტნიორი კომპანია</label>--}}
{{--                        <select  wire:model.defer="partner_status" class="form-control" id="partner_status" name="partner_status">--}}
{{--                            <option value="" selected="selected">- აირჩიეთ სტატუსი -</option>--}}
{{--                            <option value="KVIKVO">KVIKVO</option>--}}
{{--                            <option value="2SHOP">TA</option>--}}
{{--                        </select>--}}
{{--                    </div>--}}






                </div>
                <div class="form-group row">
                    <div class="col-lg-3">
                        <input id="is_foreigner" type="checkbox" wire:model.defer="is_foreigner">
                        <label for="is_foreigner" class="geo">@lang('trans.foreign_citizen')</label>
                        @error('is_foreigner') <span class="geo text-danger error">{{ $message }}</span> @enderror
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-lg-12 geo ">
                        <label>@lang('trans.terms1')</label>
                        <label class="checkbox checkbox-lg">
                            <input wire:model.defer="checkbox1" type="checkbox"  name="checkbox1">
                            <span class="text-center"></span>
                            <a href="" class="text-primary font-weight-bolder pl-5"
                               data-toggle="modal" data-target="#exampleModalCustomScrollable">
                                @lang('trans.termsAndConditions')
                            </a>
                            @error('checkbox1')
                            <strong class="text-danger"> {{ $message }}</strong>
                            @enderror
                        </label>
                    </div>
                </div>

            </div>

            <div class="card-footer">
                <div class="row">
                    <div class="col-lg-4"></div>
                    <div class="col-lg-4">
                        <center><button type="submit" class="geo btn btn-primary mr-2">@lang('trans.register')</button></center>
                        {{--                <button type="reset" class="btn btn-primary mr-2">Submit</button>--}}
                        {{--                <button type="reset" class="btn btn-secondary">Cancel</button>--}}
                    </div>
                    <div class="col-lg-4"></div>
                </div>
            </div>
        </form>


        <!--end::Form-->
    </div>


</div>


