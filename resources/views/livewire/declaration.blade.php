<div>
    <button
        class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:shadow-outline-gray disabled:opacity-25 transition ease-in-out duration-150"
        wire:click.prevent="edit({{ $productId }})">@lang('trans.declaration')
    </button>
    <div
        class="@if (!$showModal) hidden @endif flex items-center justify-center fixed left-0 bottom-0 w-full h-full bg-gray-800 bg-opacity-90">
        <div class="bg-white rounded-lg w-1/2">
            <form wire:submit.prevent="save" class="w-full">
                <div class="flex flex-col items-start p-4">
                    <div class="flex items-center w-full border-b pb-4">
                        <div class="text-gray-900 font-medium text-lg geo">
                            {{ $productId ? 'გზავნილის დეკლარაცია' : 'Add New Product' }}</div>

                        <svg wire:click="close"
                             class="ml-auto fill-current text-gray-700 w-6 h-6 cursor-pointer"
                             xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18">
                            <path
                                d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47
                                 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"/>
                        </svg>
                    </div>
{{--                    pasted--}}
                    <div class="form-group col-12 geo">
                        <label>@lang('trans.message_type')</label>
                        <div class="radio-inline">
                            <label class="radio" id="shop">
                                <input wire:click="$set('show', true)" checked="checked" type="radio" name="parcel_type"/>
                                <span></span>
                                @lang('trans.online_shop')
                            </label>
                            <label class="radio" id="personal">
                                <input wire:click="$set('show', false)"  type="radio" name="parcel_type"/>
                                <span></span>
                                @lang('trans.personal_message')
                            </label>
                        </div>
                    </div>
                    @if($show)
                        <div class="form-group col-12 geo">
                            <label>@lang('trans.tracking_code')</label>
{{--                            <input wire:model.defer="product.pid"--}}
{{--                                   class="mt-2 text-sm sm:text-base pl-2 pr-4 rounded-lg @error('product.pid') is-invalid @enderror--}}
{{--                                   border border-gray-400 w-full py-2 focus:outline-none focus:border-blue-400"/>--}}
                            <input wire:model.defer="product.tracking_code" type="text" name="tracking_code" id="tracking_code"
                                   class="form-control @error('product.tracking_code') is-invalid @enderror"/>
                            @error('product.tracking_code') <span class="geo text-danger error">{{ $message }}</span> @enderror
                        </div>
                    @endif
                    <div class="form-group col-12 geo" id="shop_name">
                        <label>ონლაინ მაღაზია</label>
                        <input wire:model.defer="product.sender_company" type="text" name="sender_company" id="sender_company"
                               class="form-control @error('product.sender_company') is-invalid @enderror"
                               placeholder="taobao.com"
                               value="taobao.com"/>
                        @error('product.sender_company') <span class="geo text-danger error">{{ $message }}</span> @enderror
                    </div>
                    <div class="form-group col-12 geo">
                        <div class="form-group">
                            <label for="exampleSelect1">@lang('trans.parcel_code')</label>
                            <select wire:model="product.category_id"
                                    class="form-control @error('product.itemcategory') is-invalid @enderror" id="exampleSelect1">
                                <option value="">@lang('trans.parcel_category')</option>
                                @foreach ($itemcategory as $itemcat)
                                    <option value="{{ $itemcat->id }}">{{$itemcat->code}} - {{$itemcat->description_ge}}</option>
                                @endforeach
                            </select>
                            @error('product.category_id') <span class="geo text-danger error">{{ $message }}</span> @enderror
                        </div>
                    </div>
{{--                    axali--}}
                    <div class="form-group col-12 geo">
                        <div class="form-group">
                            <label>@lang('trans.parcel_quantity')</label>
                            <input wire:model="product.quantity" type="number" step="1.00"
                                   class="form-control @error('product.quantity') is-invalid @enderror" placeholder=""
                                   name="client_buy_amount"
                                   id="client_buy_amount"
                                   value=""/>
                            @error('product.quantity') <span class="geo text-danger error">{{ $message }}</span> @enderror
                        </div>
                    </div>

                    <div class="form-group col-12 geo">
                        <div class="form-group">
                            <label>@lang('trans.parcel_price')</label>
                            <input wire:model="product.client_buy_amount" type="number" step="0.1"
                                   class="form-control @error('product.client_buy_amount') is-invalid @enderror" placeholder="" name="client_buy_amount"
                                   id="client_buy_amount"
                                   value=""/>
                            @error('product.client_buy_amount') <span class="geo text-danger error">{{ $message }}</span> @enderror
                        </div>


{{--                        <label class="text-danger">300 ლარზე მეტი ღირებულების ამანათისთვის შესყიდვის ინვოისის ატვირთვა სავალდებულოა</label>--}}
                        <div class="">

                            <input type="file" wire:model="cover_image">

{{--                            @error('cover_image') <span class="error">{{ $message }}</span> @enderror--}}
                            @error('cover_image') <span class="geo text-danger error">{{ $message }}</span> @enderror
                        </div>
                    </div>

                    @if(!empty($this->goods->cover_image))
                    <div class="form-group col-12 geo">
                        <p>
                            <span class="navi-icon">
                                <i class="la la-print"></i>
                            </span>
                            <a id="cover_image3" href="{{ asset('/storage/invoice_images') . '/' . $this->goods->cover_image}}" target="_blank"
                               rel="noopener noreferrer">
                                @lang('trans.invoice1') {{ $this->goods->cover_image }}
                            </a>

                        </p>
                    </div>
                    @endif
                    <div class="form-group col-12 geo">
                        <label class="col-form-label text-right geo">
                            @lang('trans.customs_clearence')
                        </label>

                            <span class="switch switch-lg switch-icon">
                                <label>
                                    <input wire:model="product.must_customize"
{{--                                           {{ $this->goods->must_customize === 1 ? 'checked' : '' }}--}}
                                           type="checkbox"
                                           value="1"
                                           name="customize" id="customize">
                                    <span></span>
                                </label>
                            </span>

                    </div>

                    <div class="ml-auto">
                        <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                type="submit">{{ $productId ? 'Save Changes' : 'Save' }}
                        </button>
                        <button class="bg-gray-500 text-white font-bold py-2 px-4 rounded"
                                wire:click="close"
                                type="button"
                                data-dismiss="modal">Close
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

</div>









